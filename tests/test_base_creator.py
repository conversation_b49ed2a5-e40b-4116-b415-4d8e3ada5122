"""
Test suite for BaseDicomCreator class.

Tests the base DICOM creator functionality including initialization,
dataset creation, validation, and save functionality.
"""

import pytest
import tempfile
from pathlib import Path
import datetime
from unittest.mock import Mock, patch

import pydicom
from pydicom.dataset import Dataset

from pyrt_dicom.core.base import BaseDicom<PERSON><PERSON>
from pyrt_dicom.uid_generation.generators import De<PERSON>ult<PERSON><PERSON>Generator
from pyrt_dicom.utils.exceptions import Dicom<PERSON><PERSON>tion<PERSON>rror, ValidationError


class TestBaseDicomCreator(BaseDicomCreator):
    """Concrete implementation of BaseDicomCreator for testing."""
    
    def _create_modality_specific_dataset(self) -> Dataset:
        """Create a minimal test dataset."""
        dataset = self._create_base_dataset()
        # Add minimal required elements for testing
        dataset.SOPClassUID = "1.2.840.10008.5.1.4.1.1.2"  # CT Image Storage
        dataset.Modality = "CT"
        return dataset
        
    def _validate_modality_specific(self) -> None:
        """Test-specific validation."""
        if hasattr(self, '_force_validation_error'):
            self._validation_errors.append("Test validation error")


class TestBaseDicomCreatorInitialization:
    """Test BaseDicomCreator initialization."""
    
    def test_basic_initialization(self):
        """Test basic initialization with no parameters."""
        creator = TestBaseDicomCreator()
        
        assert creator.reference_image is None
        assert creator.patient_info == {}
        assert creator.uid_generator is not None
        assert creator.uid_registry is not None
        assert creator.dataset is None
        assert isinstance(creator.creation_timestamp, datetime.datetime)
        assert not creator.is_validated
        assert creator.validation_errors == []
        
    def test_initialization_with_patient_info(self):
        """Test initialization with patient information."""
        patient_info = {
            'PatientID': 'RT001',
            'PatientName': 'Test^Patient',
            'PatientBirthDate': '19800101',
            'PatientSex': 'M'
        }
        
        creator = TestBaseDicomCreator(patient_info=patient_info)
        assert creator.patient_info == patient_info
        
    def test_initialization_with_reference_dataset(self):
        """Test initialization with reference DICOM dataset."""
        ref_dataset = Dataset()
        ref_dataset.StudyInstanceUID = "*******.5"
        ref_dataset.FrameOfReferenceUID = "*******.6"
        
        creator = TestBaseDicomCreator(reference_image=ref_dataset)
        assert creator.reference_image is ref_dataset
        
    def test_initialization_with_custom_uid_generator(self):
        """Test initialization with custom UID generator."""
        custom_generator = DefaultUIDGenerator()
        creator = TestBaseDicomCreator(uid_generator=custom_generator)
        assert creator.uid_generator is custom_generator


class TestReferenceImageProcessing:
    """Test reference image processing."""
    
    def test_process_reference_none(self):
        """Test processing None reference."""
        creator = TestBaseDicomCreator()
        result = creator._process_reference_image(None)
        assert result is None
        
    def test_process_reference_dataset(self):
        """Test processing Dataset reference."""
        dataset = Dataset()
        dataset.PatientID = "TEST001"
        
        creator = TestBaseDicomCreator()
        result = creator._process_reference_image(dataset)
        assert result is dataset
        
    def test_process_reference_invalid_path(self):
        """Test processing invalid file path."""
        creator = TestBaseDicomCreator()
        
        with pytest.raises(DicomCreationError, match="Cannot load reference image"):
            creator._process_reference_image("nonexistent_file.dcm")
            
    def test_process_reference_invalid_type(self):
        """Test processing invalid reference type."""
        creator = TestBaseDicomCreator()
        
        with pytest.raises(DicomCreationError, match="Invalid reference image type"):
            creator._process_reference_image(123)


class TestBaseDatasetCreation:
    """Test base DICOM dataset creation."""
    
    def test_create_base_dataset(self):
        """Test basic dataset creation."""
        creator = TestBaseDicomCreator()
        dataset = creator._create_base_dataset()
        
        # Check that dataset is created
        assert isinstance(dataset, Dataset)
        
        # Check required UIDs are generated
        assert hasattr(dataset, 'StudyInstanceUID')
        assert hasattr(dataset, 'SeriesInstanceUID') 
        assert hasattr(dataset, 'SOPInstanceUID')
        assert hasattr(dataset, 'FrameOfReferenceUID')
        
        # Check UID format compliance
        assert len(dataset.StudyInstanceUID) <= 64
        assert len(dataset.SeriesInstanceUID) <= 64
        assert len(dataset.SOPInstanceUID) <= 64
        assert len(dataset.FrameOfReferenceUID) <= 64
        
        # Check required elements are present
        assert hasattr(dataset, 'PatientName')
        assert hasattr(dataset, 'PatientID')
        assert hasattr(dataset, 'InstanceCreationDate')
        assert hasattr(dataset, 'InstanceCreationTime')
        assert hasattr(dataset, 'StudyDate')
        assert hasattr(dataset, 'StudyTime')
        assert hasattr(dataset, 'SeriesNumber')
        assert hasattr(dataset, 'InstanceNumber')
        assert hasattr(dataset, 'Manufacturer')
        
        # Check default values
        assert dataset.Manufacturer == "pyrt-dicom"
        assert dataset.SeriesNumber == "1"
        assert dataset.InstanceNumber == "1"
        assert dataset.StudyID == "1"
        
    def test_uid_registry_population(self):
        """Test that UIDs are properly registered."""
        creator = TestBaseDicomCreator()
        dataset = creator._create_base_dataset()
        
        # Check that UIDs are registered in registry by checking relationships
        series = creator.uid_registry.get_study_series(dataset.StudyInstanceUID)
        assert len(series) == 1
        assert dataset.SeriesInstanceUID in series
        
        instances = creator.uid_registry.get_series_instances(dataset.SeriesInstanceUID)
        assert len(instances) == 1
        assert dataset.SOPInstanceUID in instances
        
        # Check frame references
        frame_refs = creator.uid_registry.get_study_frame_references(dataset.StudyInstanceUID)
        assert len(frame_refs) == 1
        assert dataset.FrameOfReferenceUID in frame_refs


class TestPatientInfoHandling:
    """Test patient information handling."""
    
    def test_set_patient_info_normal(self):
        """Test setting normal patient information."""
        patient_info = {
            'PatientID': 'RT001',
            'PatientName': 'Test^Patient',
            'PatientBirthDate': '19800101',
            'PatientSex': 'M',
            'PatientAge': '043Y',
            'PatientWeight': '70.0',
            'PatientSize': '170.0',
            'PatientComments': 'Test patient'
        }
        
        creator = TestBaseDicomCreator(patient_info=patient_info)
        dataset = creator._create_base_dataset()
        creator._set_patient_info(dataset)
        
        assert dataset.PatientID == 'RT001'
        assert dataset.PatientName == 'Test^Patient'
        assert dataset.PatientBirthDate == '19800101'
        assert dataset.PatientSex == 'M'
        assert dataset.PatientAge == '043Y'
        assert dataset.PatientWeight == '70.0'
        assert dataset.PatientSize == '170.0'
        assert dataset.PatientComments == 'Test patient'
        
    def test_set_patient_info_empty(self):
        """Test setting patient info with empty dictionary."""
        creator = TestBaseDicomCreator()
        dataset = creator._create_base_dataset()
        creator._set_patient_info(dataset)
        
        assert dataset.PatientID == ''
        assert dataset.PatientName == ''
        assert dataset.PatientBirthDate == ''
        assert dataset.PatientSex == ''
        
    def test_set_patient_info_anonymized(self):
        """Test anonymized patient information."""
        patient_info = {
            'PatientID': 'RT001',
            'PatientName': 'Test^Patient',
            'PatientBirthDate': '19800101'
        }
        
        creator = TestBaseDicomCreator(patient_info=patient_info)
        dataset = creator._create_base_dataset()
        creator._set_patient_info(dataset, anonymize=True)
        
        assert dataset.PatientID == 'ANON'
        assert dataset.PatientName == 'ANONYMOUS'
        assert dataset.PatientBirthDate == ''


class TestStudyInfoHandling:
    """Test study and series information handling."""
    
    def test_set_study_info_defaults(self):
        """Test setting study info with defaults."""
        creator = TestBaseDicomCreator()
        dataset = creator._create_base_dataset()
        creator._set_study_info(dataset)
        
        assert dataset.StudyDescription == 'RT Study'
        assert dataset.SeriesDescription == 'RT Series'
        
    def test_set_study_info_with_patient_info(self):
        """Test setting study info from patient_info."""
        patient_info = {
            'StudyDescription': 'Custom Study',
            'SeriesDescription': 'Custom Series',
            'InstitutionName': 'Test Hospital',
            'DepartmentName': 'Radiation Oncology'
        }
        
        creator = TestBaseDicomCreator(patient_info=patient_info)
        dataset = creator._create_base_dataset()
        creator._set_study_info(dataset)
        
        assert dataset.StudyDescription == 'Custom Study'
        assert dataset.SeriesDescription == 'Custom Series'
        assert dataset.InstitutionName == 'Test Hospital'
        assert dataset.InstitutionalDepartmentName == 'Radiation Oncology'
        
    def test_set_study_info_with_reference(self):
        """Test study info with reference image."""
        # Create reference dataset
        ref_dataset = Dataset()
        ref_dataset.StudyInstanceUID = "*******.5.6"
        ref_dataset.StudyDescription = "Reference Study"
        ref_dataset.FrameOfReferenceUID = "*******.5.7"
        
        creator = TestBaseDicomCreator(reference_image=ref_dataset)
        dataset = creator._create_base_dataset()
        original_study_uid = dataset.StudyInstanceUID
        
        creator._set_study_info(dataset)
        
        # Should use reference study UID
        assert dataset.StudyInstanceUID == ref_dataset.StudyInstanceUID
        assert dataset.StudyDescription == "Reference Study"
        assert dataset.FrameOfReferenceUID == ref_dataset.FrameOfReferenceUID


class TestValidation:
    """Test validation functionality."""
    
    def test_validation_success(self):
        """Test successful validation."""
        creator = TestBaseDicomCreator(patient_info={'PatientID': 'RT001'})
        
        # Should not raise exception
        creator.validate()
        assert creator.is_validated
        assert creator.validation_errors == []
        
    def test_validation_missing_patient_id(self):
        """Test validation failure for missing PatientID."""
        creator = TestBaseDicomCreator()
        
        with pytest.raises(ValidationError, match="PatientID is required"):
            creator.validate()
            
        assert not creator.is_validated
        assert "PatientID is required" in creator.validation_errors
        
    def test_validation_modality_specific_error(self):
        """Test validation with modality-specific error."""
        creator = TestBaseDicomCreator(patient_info={'PatientID': 'RT001'})
        creator._force_validation_error = True
        
        with pytest.raises(ValidationError, match="Test validation error"):
            creator.validate()
            
        assert "Test validation error" in creator.validation_errors


class TestSaveFunctionality:
    """Test DICOM file saving."""
    
    def test_save_basic(self):
        """Test basic file saving."""
        creator = TestBaseDicomCreator(patient_info={'PatientID': 'RT001'})
        
        with tempfile.TemporaryDirectory() as temp_dir:
            output_path = Path(temp_dir) / "test.dcm"
            
            saved_path = creator.save(output_path)
            
            assert saved_path == output_path
            assert output_path.exists()
            assert output_path.stat().st_size > 0
            
            # Verify file can be read back
            loaded_dataset = pydicom.dcmread(output_path)
            assert loaded_dataset.PatientID == 'RT001'
            assert loaded_dataset.Manufacturer == "pyrt-dicom"
            
    def test_save_without_validation(self):
        """Test saving without validation."""
        creator = TestBaseDicomCreator()  # No PatientID
        
        with tempfile.TemporaryDirectory() as temp_dir:
            output_path = Path(temp_dir) / "test.dcm"
            
            # Should succeed when validation is disabled
            creator.save(output_path, validate=False)
            assert output_path.exists()
            
    def test_save_with_validation_failure(self):
        """Test save failure due to validation."""
        creator = TestBaseDicomCreator()  # No PatientID
        
        with tempfile.TemporaryDirectory() as temp_dir:
            output_path = Path(temp_dir) / "test.dcm"
            
            with pytest.raises(ValidationError):
                creator.save(output_path, validate=True)
                
    def test_save_with_anonymization(self):
        """Test saving with anonymization."""
        creator = TestBaseDicomCreator(
            patient_info={'PatientID': 'RT001', 'PatientName': 'Test^Patient'}
        )
        
        with tempfile.TemporaryDirectory() as temp_dir:
            output_path = Path(temp_dir) / "test.dcm"
            
            creator.save(output_path, anonymize=True)
            
            # Verify anonymization
            loaded_dataset = pydicom.dcmread(output_path)
            assert loaded_dataset.PatientID == 'ANON'
            assert loaded_dataset.PatientName == 'ANONYMOUS'
            
    def test_save_creates_directories(self):
        """Test that save creates output directories."""
        creator = TestBaseDicomCreator(patient_info={'PatientID': 'RT001'})
        
        with tempfile.TemporaryDirectory() as temp_dir:
            output_path = Path(temp_dir) / "subdir" / "nested" / "test.dcm"
            
            creator.save(output_path)
            
            assert output_path.exists()
            assert output_path.parent.exists()
            
    def test_save_file_meta_information(self):
        """Test that file meta information is properly set."""
        creator = TestBaseDicomCreator(patient_info={'PatientID': 'RT001'})
        
        with tempfile.TemporaryDirectory() as temp_dir:
            output_path = Path(temp_dir) / "test.dcm"
            
            creator.save(output_path)
            
            loaded_dataset = pydicom.dcmread(output_path)
            assert hasattr(loaded_dataset, 'file_meta')
            assert loaded_dataset.file_meta.MediaStorageSOPClassUID == loaded_dataset.SOPClassUID
            assert loaded_dataset.file_meta.MediaStorageSOPInstanceUID == loaded_dataset.SOPInstanceUID
            assert "pyrt-dicom" in loaded_dataset.file_meta.ImplementationVersionName


class TestProperties:
    """Test class properties and string representation."""
    
    def test_properties_initial_state(self):
        """Test properties in initial state."""
        creator = TestBaseDicomCreator()
        
        assert not creator.is_validated
        assert creator.validation_errors == []
        
    def test_properties_after_validation(self):
        """Test properties after validation."""
        creator = TestBaseDicomCreator(patient_info={'PatientID': 'RT001'})
        creator.validate()
        
        assert creator.is_validated
        assert creator.validation_errors == []
        
    def test_properties_after_failed_validation(self):
        """Test properties after failed validation."""
        creator = TestBaseDicomCreator()
        
        try:
            creator.validate()
        except ValidationError:
            pass
            
        assert not creator.is_validated
        assert len(creator.validation_errors) > 0
        
    def test_repr(self):
        """Test string representation."""
        creator = TestBaseDicomCreator(patient_info={'PatientID': 'RT001'})
        repr_str = repr(creator)
        
        assert "TestBaseDicomCreator" in repr_str
        assert "PatientID=RT001" in repr_str
        assert "no reference" in repr_str
        
        # With reference
        ref_dataset = Dataset()
        creator_with_ref = TestBaseDicomCreator(
            patient_info={'PatientID': 'RT001'},
            reference_image=ref_dataset
        )
        repr_str = repr(creator_with_ref)
        assert "with reference" in repr_str