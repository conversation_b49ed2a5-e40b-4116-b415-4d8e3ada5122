"""
Test module imports for pyrt-dicom core structure.

Tests the import structure created in Subtask 1.1.1 to verify that:
- Core module structure works without errors
- Utils package imports work correctly  
- Exception hierarchy is properly accessible
- Logging framework can be imported and used
"""

import pytest


def test_main_package_imports():
    """Test that main package imports work without errors."""
    import pyrt_dicom
    
    # Check metadata is accessible
    assert hasattr(pyrt_dicom, '__version__')
    assert hasattr(pyrt_dicom, '__author__')
    assert hasattr(pyrt_dicom, '__email__')
    
    # Check version format
    assert isinstance(pyrt_dicom.__version__, str)
    assert len(pyrt_dicom.__version__.split('.')) >= 2  # At least major.minor


def test_utils_exceptions_import():
    """Test that exception classes can be imported."""
    from pyrt_dicom.utils import (
        PyrtDicomError,
        DicomCreationError,
        ValidationError,
        CoordinateSystemError,
        UIDGenerationError,
        TemplateError
    )
    
    # Check inheritance hierarchy
    assert issubclass(DicomCreationError, PyrtDicomError)
    assert issubclass(ValidationError, PyrtDicomError)
    assert issubclass(CoordinateSystemError, PyrtDicomError)
    assert issubclass(UIDGenerationError, PyrtDicomError)
    assert issubclass(TemplateError, PyrtDicomError)


def test_utils_logging_import():
    """Test that logging utilities can be imported."""
    from pyrt_dicom.utils import (
        ClinicalFormatter,
        get_clinical_logger,
        log_dicom_creation,
        log_validation_result
    )
    
    # Test basic logger creation
    logger = get_clinical_logger('test')
    assert logger is not None
    assert logger.name == 'test'


def test_core_package_import():
    """Test that core package can be imported (even though empty)."""
    import pyrt_dicom.core
    
    # Should not raise ImportError
    assert pyrt_dicom.core is not None


def test_direct_package_imports():
    """Test importing utilities directly from main package."""
    import pyrt_dicom
    
    # Test exception classes are available at top level
    assert hasattr(pyrt_dicom, 'PyrtDicomError')
    assert hasattr(pyrt_dicom, 'DicomCreationError')
    assert hasattr(pyrt_dicom, 'ValidationError')
    assert hasattr(pyrt_dicom, 'CoordinateSystemError')
    assert hasattr(pyrt_dicom, 'UIDGenerationError')
    assert hasattr(pyrt_dicom, 'TemplateError')
    
    # Test logging functions are available at top level
    assert hasattr(pyrt_dicom, 'get_clinical_logger')
    assert hasattr(pyrt_dicom, 'log_dicom_creation')
    assert hasattr(pyrt_dicom, 'log_validation_result')


def test_exception_functionality():
    """Test that custom exceptions work correctly."""
    from pyrt_dicom import ValidationError
    
    # Test exception can be raised and caught
    with pytest.raises(ValidationError):
        raise ValidationError("Test validation error")
    
    # Test exception message
    try:
        raise ValidationError("Custom message")
    except ValidationError as e:
        assert str(e) == "Custom message"


def test_logging_functionality():
    """Test basic logging functionality works."""
    from pyrt_dicom import get_clinical_logger
    import logging
    
    # Create logger and check basic properties
    logger = get_clinical_logger('test_module')
    assert isinstance(logger, logging.Logger)
    assert logger.name == 'test_module'
    assert logger.level == logging.INFO


if __name__ == '__main__':
    pytest.main([__file__])