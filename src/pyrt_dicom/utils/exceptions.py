"""
Custom exception hierarchy for pyrt-dicom.

Provides RT-specific exceptions with clinical context and helpful error messages
for medical physics workflows.
"""


class PyrtDicomError(Exception):
    """Base exception class for all pyrt-dicom errors."""
    pass


class DicomCreationError(PyrtDicomError):
    """Raised when DICOM file creation fails.
    
    This covers errors in the DICOM creation process, including invalid
    data structures, missing required elements, or pydicom integration issues.
    """
    pass


class ValidationError(PyrtDicomError):
    """Raised when validation of clinical or technical parameters fails.
    
    Used for both clinical validation (dose ranges, geometric constraints)
    and DICOM standard compliance validation.
    """
    pass


class CoordinateSystemError(PyrtDicomError):
    """Raised when coordinate system transformations or validations fail.
    
    This includes frame of reference inconsistencies, geometric transformation
    errors, and spatial relationship validation failures.
    """
    pass


class UIDGenerationError(PyrtDicomError):
    """Raised when UID generation or management fails.
    
    Covers UID format errors, uniqueness violations, and UID relationship
    consistency issues.
    """
    pass


class TemplateError(PyrtDicomError):
    """Raised when DICOM template or IOD structure errors occur.
    
    Used for missing template elements, invalid IOD structures, or
    modality-specific template issues.
    """
    pass