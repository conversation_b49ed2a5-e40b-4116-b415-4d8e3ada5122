"""
Base DICOM Creator Framework for Radiotherapy DICOM Objects.

Provides the foundational BaseDicomCreator class that all RT DICOM creators inherit from,
implementing common functionality for patient information, reference image handling,
UID management, validation, and file saving.

## Clinical Usage

The BaseDicomCreator provides a consistent foundation for creating all RT DICOM types:

```python
import pyrt_dicom as prt

# Create derived classes like RTStructureSet, RTDose, etc.
creator = SomeRTCreator(
    reference_image=ct_reference,
    patient_info={'PatientID': 'RT001', 'PatientName': 'Test^Patient'}
)

# Validate before saving (optional but recommended)
creator.validate()

# Save with automatic validation
creator.save('output.dcm')
```

## Architecture

BaseDicomCreator follows the Template Method pattern:
- Common functionality is implemented in the base class
- Modality-specific behavior is implemented by derived classes
- Validation and saving workflows are standardized across all RT types
"""

from abc import ABC, abstractmethod
from pathlib import Path
from typing import Dict, Any, Optional, Union
import datetime

import pydicom
from pydicom.dataset import Dataset, FileMetaDataset
from pydicom.uid import generate_uid

from ..uid_generation.generators import DefaultUIDGenerator
from ..uid_generation.registry import UIDRegistry
from ..utils.exceptions import DicomCreationError, ValidationError
from ..utils.logging import get_clinical_logger


class BaseDicomCreator(ABC):
    """
    Base class for all DICOM RT creators.
    
    Provides common functionality for DICOM creation including patient information
    handling, UID management, validation framework, and file saving capabilities.
    
    All RT DICOM creators (CTSeries, RTStructureSet, RTDose, RTPlan) inherit from
    this class and implement modality-specific behavior.
    """
    
    def __init__(self, 
                 reference_image: Optional[Union[Dataset, str, Path]] = None,
                 patient_info: Optional[Dict[str, Any]] = None,
                 uid_generator: Optional[Any] = None):
        """
        Initialize base DICOM creator.
        
        Parameters
        ----------
        reference_image : Dataset, str, Path, optional
            Reference CT image or path to reference DICOM file for geometric alignment
        patient_info : dict, optional
            Patient information dictionary with keys like 'PatientID', 'PatientName', etc.
        uid_generator : UIDGenerator, optional
            UID generation strategy. If None, uses DefaultUIDGenerator
            
        Examples
        --------
        >>> creator = BaseDicomCreator(
        ...     reference_image=ct_dataset,
        ...     patient_info={'PatientID': 'RT001', 'PatientName': 'Test^Patient'}
        ... )
        """
        # Core components
        self.reference_image = self._process_reference_image(reference_image)
        self.patient_info = patient_info or {}
        self.uid_generator = uid_generator or DefaultUIDGenerator.create_default_generator()
        self.uid_registry = UIDRegistry()
        self.logger = get_clinical_logger(__name__)
        
        # DICOM dataset - will be created by derived classes
        self.dataset: Optional[Dataset] = None
        
        # Generation timestamp for audit trail
        self.creation_timestamp = datetime.datetime.now()
        
        # Validation state
        self._is_validated = False
        self._validation_errors: list[str] = []
    
    def _process_reference_image(self, reference: Optional[Union[Dataset, str, Path]]) -> Optional[Dataset]:
        """
        Process reference image input into a pydicom Dataset.
        
        Parameters
        ----------
        reference : Dataset, str, Path, or None
            Reference image specification
            
        Returns
        -------
        Dataset or None
            Processed reference image dataset
            
        Raises
        ------
        DicomCreationError
            If reference image cannot be loaded or processed
        """
        if reference is None:
            return None
            
        if isinstance(reference, Dataset):
            return reference
            
        if isinstance(reference, (str, Path)):
            try:
                return pydicom.dcmread(reference)
            except Exception as e:
                raise DicomCreationError(f"Cannot load reference image from {reference}: {e}")
                
        raise DicomCreationError(f"Invalid reference image type: {type(reference)}")
    
    def _create_base_dataset(self) -> Dataset:
        """
        Create base DICOM dataset with required elements.
        
        This method creates a minimal DICOM dataset with mandatory elements
        that are common to all RT DICOM types. Derived classes should call
        this method and then add modality-specific elements.
        
        Returns
        -------
        Dataset
            Base DICOM dataset with required elements
            
        Examples
        --------
        >>> creator = SomeRTCreator()
        >>> dataset = creator._create_base_dataset()
        >>> print(dataset.SOPClassUID)
        """
        dataset = Dataset()
        
        # Generate core UIDs
        study_uid = self.uid_generator.generate_study_instance_uid()
        series_uid = self.uid_generator.generate_series_instance_uid()
        instance_uid = self.uid_generator.generate_sop_instance_uid()
        frame_of_ref_uid = self.uid_generator.generate_frame_of_reference_uid()
        
        # Register UID relationships
        self.uid_registry.register_study_uid(study_uid)
        self.uid_registry.register_series_uid(series_uid, study_uid)
        self.uid_registry.register_instance_uid(instance_uid, series_uid)
        self.uid_registry.register_frame_reference_uid(frame_of_ref_uid, study_uid)
        
        # Core Instance Module (C.12.1)
        dataset.InstanceCreationDate = self.creation_timestamp.strftime('%Y%m%d')
        dataset.InstanceCreationTime = self.creation_timestamp.strftime('%H%M%S.%f')[:10]
        dataset.InstanceCreatorUID = self.uid_generator.root_uid
        
        # Patient Module (C.7.1.1) - will be populated by _set_patient_info
        dataset.PatientName = ""
        dataset.PatientID = ""
        dataset.PatientBirthDate = ""
        dataset.PatientSex = ""
        
        # General Study Module (C.7.2.1)  
        dataset.StudyInstanceUID = study_uid
        dataset.StudyDate = self.creation_timestamp.strftime('%Y%m%d')
        dataset.StudyTime = self.creation_timestamp.strftime('%H%M%S.%f')[:10]
        dataset.ReferringPhysicianName = ""
        dataset.StudyID = "1"
        dataset.AccessionNumber = ""
        
        # General Series Module (C.7.3.1)
        dataset.SeriesInstanceUID = series_uid
        dataset.SeriesNumber = "1"
        dataset.SeriesDate = self.creation_timestamp.strftime('%Y%m%d')
        dataset.SeriesTime = self.creation_timestamp.strftime('%H%M%S.%f')[:10]
        
        # General Equipment Module (C.7.5.1)
        dataset.Manufacturer = "pyrt-dicom"
        dataset.ManufacturerModelName = "pyrt-dicom DICOM Creator"
        dataset.SoftwareVersions = "1.0.0"  # TODO: Get from package version
        
        # Frame of Reference Module (C.7.4.1)
        dataset.FrameOfReferenceUID = frame_of_ref_uid
        dataset.PositionReferenceIndicator = ""
        
        # General Image Module (C.7.6.1) - basic elements
        dataset.InstanceNumber = "1"
        dataset.ContentDate = self.creation_timestamp.strftime('%Y%m%d')
        dataset.ContentTime = self.creation_timestamp.strftime('%H%M%S.%f')[:10]
        
        # SOP Common Module (C.12.1)
        dataset.SOPInstanceUID = instance_uid
        # SOPClassUID will be set by derived classes
        
        return dataset
    
    def _set_patient_info(self, dataset: Dataset, anonymize: bool = False) -> None:
        """
        Set patient information in DICOM dataset.
        
        Parameters
        ----------
        dataset : Dataset
            DICOM dataset to modify
        anonymize : bool, default False
            Whether to anonymize patient information
            
        Examples
        --------
        >>> creator = SomeRTCreator(patient_info={'PatientID': 'RT001'})
        >>> dataset = creator._create_base_dataset()
        >>> creator._set_patient_info(dataset)
        >>> print(dataset.PatientID)
        RT001
        """
        if anonymize:
            # Basic anonymization - more comprehensive anonymization in future Phase 4
            dataset.PatientName = "ANONYMOUS"
            dataset.PatientID = "ANON"
            dataset.PatientBirthDate = ""
        else:
            # Set patient information from provided data or defaults
            dataset.PatientName = self.patient_info.get('PatientName', '')
            dataset.PatientID = self.patient_info.get('PatientID', '')
            dataset.PatientBirthDate = self.patient_info.get('PatientBirthDate', '')
            dataset.PatientSex = self.patient_info.get('PatientSex', '')
            dataset.PatientAge = self.patient_info.get('PatientAge', '')
            dataset.PatientWeight = self.patient_info.get('PatientWeight', '')
            dataset.PatientSize = self.patient_info.get('PatientSize', '')
            
            # Additional patient information if available
            if 'PatientComments' in self.patient_info:
                dataset.PatientComments = self.patient_info['PatientComments']
                
    def _set_study_info(self, dataset: Dataset) -> None:
        """
        Set study and series information in DICOM dataset.
        
        Parameters
        ----------
        dataset : Dataset
            DICOM dataset to modify
            
        Examples
        --------
        >>> creator = SomeRTCreator()
        >>> dataset = creator._create_base_dataset()  
        >>> creator._set_study_info(dataset)
        """
        # Study information (can be overridden by patient_info)
        dataset.StudyDescription = self.patient_info.get('StudyDescription', 'RT Study')
        dataset.SeriesDescription = self.patient_info.get('SeriesDescription', 'RT Series')
        dataset.OperatorsName = self.patient_info.get('OperatorsName', '')
        dataset.PhysiciansOfRecord = self.patient_info.get('PhysiciansOfRecord', '')
        
        # Institution information
        dataset.InstitutionName = self.patient_info.get('InstitutionName', '')
        dataset.InstitutionAddress = self.patient_info.get('InstitutionAddress', '')
        dataset.InstitutionalDepartmentName = self.patient_info.get('DepartmentName', '')
        
        # If reference image is provided, align study information
        if self.reference_image:
            # Match study UID if available (for consistency with CT reference)
            if hasattr(self.reference_image, 'StudyInstanceUID'):
                # Use reference study UID for consistency
                dataset.StudyInstanceUID = self.reference_image.StudyInstanceUID
                # Re-register with new study UID
                self.uid_registry.register_study_uid(dataset.StudyInstanceUID)
                
            # Copy relevant study information from reference
            if hasattr(self.reference_image, 'StudyDescription'):
                if not dataset.StudyDescription or dataset.StudyDescription == 'RT Study':
                    dataset.StudyDescription = self.reference_image.StudyDescription
                    
            # Match frame of reference UID for geometric consistency
            if hasattr(self.reference_image, 'FrameOfReferenceUID'):
                dataset.FrameOfReferenceUID = self.reference_image.FrameOfReferenceUID
                
    @abstractmethod
    def _create_modality_specific_dataset(self) -> Dataset:
        """
        Create modality-specific DICOM dataset.
        
        This abstract method must be implemented by derived classes to create
        the modality-specific portions of the DICOM dataset (CT, RTSTRUCT, etc.).
        
        Returns
        -------
        Dataset
            Complete DICOM dataset with modality-specific elements
        """
        pass
    
    def validate(self) -> None:
        """
        Comprehensive validation before saving.
        
        Validates both clinical parameters and DICOM standard compliance.
        Should be called before save() to ensure data integrity.
        
        Raises
        ------
        ValidationError
            If validation fails with details of specific issues
            
        Examples
        --------
        >>> creator = SomeRTCreator()
        >>> creator.validate()  # Raises ValidationError if issues found
        >>> creator.save('output.dcm')  # Safe to save after validation
        """
        self._validation_errors = []
        
        # Basic validation
        if not self.patient_info.get('PatientID'):
            self._validation_errors.append("PatientID is required")
            
        # Modality-specific validation (implemented by derived classes)
        self._validate_modality_specific()
        
        # Set validation state
        if self._validation_errors:
            self._is_validated = False
            error_msg = "Validation failed:\n" + "\n".join(f"- {err}" for err in self._validation_errors)
            raise ValidationError(error_msg)
        else:
            self._is_validated = True
            
    @abstractmethod  
    def _validate_modality_specific(self) -> None:
        """
        Perform modality-specific validation.
        
        Derived classes should implement this method to add validation errors
        to self._validation_errors list for any modality-specific issues.
        """
        pass
    
    def save(self, 
             filepath: Union[str, Path], 
             validate: bool = True,
             anonymize: bool = False) -> Path:
        """
        Save DICOM file with optional validation and anonymization.
        
        Parameters
        ----------
        filepath : str or Path
            Output file path for DICOM file
        validate : bool, default True
            Whether to validate before saving
        anonymize : bool, default False
            Whether to anonymize patient information
            
        Returns
        -------
        Path
            Path to saved DICOM file
            
        Raises
        ------
        ValidationError
            If validation is enabled and fails
        DicomCreationError
            If file writing fails
            
        Examples
        --------
        >>> creator = SomeRTCreator()
        >>> output_path = creator.save('output.dcm')
        >>> print(f"Saved to: {output_path}")
        """
        filepath = Path(filepath)
        
        # Validation if requested
        if validate:
            self.validate()
            
        # Create the complete dataset if not already done
        if self.dataset is None:
            self.dataset = self._create_modality_specific_dataset()
            
        # Set patient and study information
        self._set_patient_info(self.dataset, anonymize=anonymize)
        self._set_study_info(self.dataset)
        
        # Create file meta information
        file_meta = FileMetaDataset()
        file_meta.MediaStorageSOPClassUID = self.dataset.SOPClassUID
        file_meta.MediaStorageSOPInstanceUID = self.dataset.SOPInstanceUID
        file_meta.ImplementationClassUID = self.uid_generator.root_uid
        file_meta.ImplementationVersionName = "pyrt-dicom 1.0"
        file_meta.TransferSyntaxUID = pydicom.uid.ExplicitVRLittleEndian
        
        self.dataset.file_meta = file_meta
        
        # Ensure output directory exists
        filepath.parent.mkdir(parents=True, exist_ok=True)
        
        # Write DICOM file
        try:
            pydicom.dcmwrite(filepath, self.dataset, write_like_original=False)
            
            # Log successful creation
            self.logger.info(
                "DICOM file created successfully",
                extra={
                    'patient_id': self.dataset.PatientID,
                    'sop_class_uid': self.dataset.SOPClassUID,
                    'sop_instance_uid': self.dataset.SOPInstanceUID,
                    'output_path': str(filepath),
                    'anonymized': anonymize,
                    'file_size_bytes': filepath.stat().st_size
                }
            )
            
            return filepath
            
        except Exception as e:
            error_msg = f"Failed to write DICOM file to {filepath}: {e}"
            self.logger.error(error_msg, extra={'output_path': str(filepath)})
            raise DicomCreationError(error_msg)
    
    @property
    def is_validated(self) -> bool:
        """Check if the object has been validated."""
        return self._is_validated
        
    @property
    def validation_errors(self) -> list[str]:
        """Get list of validation errors from last validation attempt."""
        return self._validation_errors.copy()
        
    def __repr__(self) -> str:
        """String representation for debugging."""
        class_name = self.__class__.__name__
        patient_id = self.patient_info.get('PatientID', 'N/A')
        ref_img = "with reference" if self.reference_image is not None else "no reference"
        return f"{class_name}(PatientID={patient_id}, {ref_img})"